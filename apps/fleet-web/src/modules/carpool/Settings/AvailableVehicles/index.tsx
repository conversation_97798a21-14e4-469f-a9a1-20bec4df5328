import { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import {
  Autocomplete,
  DataGrid,
  GridActionsCellItem,
  GridRowModes,
  LinearProgress,
  Switch,
  TextField,
  Tooltip,
  useGridApiRef,
  type GridColDef,
  type GridRowId,
  type GridRowModel,
  type GridRowModesModel,
} from '@karoo-ui/core'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import { useHistory } from 'react-router'

import { getFacilitiesTranslatorFn } from 'duxs/user'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import {
  getCarpoolEditVehicle,
  getCompanyName,
} from 'src/duxs/user-sensitive-selectors'
import { getVehicleDetailsModalMainPath } from 'src/modules/app/GlobalModals/VehicleDetails/utils'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { CellTextWithMore } from 'src/util-components/CellTextWithMore'
import { ctIntl } from 'src/util-components/ctIntl'

import {
  useAvailableVehiclesQuery,
  useBookingTypesForAvailableVehiclesQuery,
  useUpdateVehicleAvailabilityMutation,
  useUpdateVehicleBookingTypesMutation,
  useUpdateVehicleCommonPoolMutation,
  type AvailableVehicle,
} from './api/queries'

const AvailableVehicles = () => {
  const apiRef = useGridApiRef()
  const availableVehiclesQuery = useAvailableVehiclesQuery()
  const availableBookingTypesQuery = useBookingTypesForAvailableVehiclesQuery()
  const updateVehicleAvailabilityMutation = useUpdateVehicleAvailabilityMutation()
  const updateVehicleBookingTypeMutation = useUpdateVehicleBookingTypesMutation()
  const updateVehicleCommonPoolMutation = useUpdateVehicleCommonPoolMutation()
  const history = useHistory()
  const companyName = useTypedSelector(getCompanyName)
  const isSCDF = companyName === 'SCDF'
  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const canEditVehicle = useTypedSelector(getCarpoolEditVehicle)

  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})

  // const handleRowEditStop: GridEventListener<'rowEditStop'> = (params, event) => {
  //   if (params.reason === GridRowEditStopReasons.rowFocusOut) {
  //     event.defaultMuiPrevented = true
  //   }
  // }

  const handleEditClick = useCallback((id: GridRowId) => {
    setRowModesModel((prev) => ({ ...prev, [id]: { mode: GridRowModes.Edit } }))
  }, [])

  const handleSaveClick = useCallback((id: GridRowId) => {
    //TODO:
    setRowModesModel((prev) => ({ ...prev, [id]: { mode: GridRowModes.View } }))
  }, [])

  const handleCancelClick = useCallback((id: GridRowId) => {
    setRowModesModel((prev) => ({
      ...prev,
      [id]: { mode: GridRowModes.View, ignoreModifications: true },
    }))
  }, [])

  const handleRowModesModelChange = (newRowModesModel: GridRowModesModel) => {
    setRowModesModel(newRowModesModel)
  }

  const rows = useMemo(
    () => availableVehiclesQuery.data ?? [],
    [availableVehiclesQuery.data],
  )

  const processRowUpdate = useCallback(
    async (
      newRow: GridRowModel<AvailableVehicle>,
      oldRow: GridRowModel<AvailableVehicle>,
    ) =>
      new Promise<AvailableVehicle>((resolve, _reject) => {
        const rollbackChanges = () => {
          resolve(oldRow)
          enqueueSnackbarWithCloseAction(
            ctIntl.formatMessage({ id: 'Failed to update' }),
            { variant: 'error' },
          )
        }

        if (newRow.commonPool !== oldRow.commonPool) {
          updateVehicleCommonPoolMutation.mutate(
            {
              vehicle: newRow.id,
              commonPool: newRow.commonPool,
            },
            {
              onSuccess: () => {
                resolve(newRow)
              },
              onError: () => rollbackChanges,
            },
          )
        }

        updateVehicleBookingTypeMutation.mutate(
          {
            vehicleId: newRow.id,
            bookingTypeIds: newRow.availableFor,
          },
          {
            onSuccess: () => {
              resolve(newRow)
            },
            onError: () => rollbackChanges,
          },
        )
      }),
    [updateVehicleBookingTypeMutation, updateVehicleCommonPoolMutation],
  )

  const bookingTypesOptions = useMemo(() => {
    if (availableBookingTypesQuery.data === undefined) {
      return []
    }
    return availableBookingTypesQuery.data
  }, [availableBookingTypesQuery.data])

  const columns = useMemo((): Array<GridColDef<AvailableVehicle>> => {
    const allColumns: Array<GridColDef<AvailableVehicle>> = [
      {
        field: 'registration',
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        valueGetter: (_, row) => row.vehicle,
        flex: 1,
      },
      {
        field: 'vehicleName',
        headerName: ctIntl.formatMessage({ id: 'Vehicle Name' }),
        valueGetter: (_, row) => row.vehicleName,
        flex: 1,
      },
      {
        field: 'description',
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        valueGetter: (_, row) => row.clientVehicleDescription,
        flex: 1,
      },
      {
        field: 'description2',
        headerName: ctIntl.formatMessage({ id: 'Description 2' }),
        valueGetter: (_, row) => row.clientVehicleDescription1,
        flex: 1,
      },
      {
        field: 'description3',
        headerName: ctIntl.formatMessage({ id: 'Description 3' }),
        valueGetter: (_, row) => row.clientVehicleDescription2,
        flex: 1,
      },
      {
        field: 'manufacturer',
        headerName: ctIntl.formatMessage({ id: 'Manufacturer' }),
        valueGetter: (_, row) => row.manufacturer,
        flex: 1,
      },
      {
        field: 'model',
        headerName: ctIntl.formatMessage({ id: 'Model' }),
        valueGetter: (_, row) => row.model,
        flex: 1,
      },
      {
        field: 'year',
        headerName: ctIntl.formatMessage({ id: 'Year' }),
        valueGetter: (_, row) => row.modelYear?.toString(),
        flex: 1,
      },
      {
        field: 'colour',
        headerName: ctIntl.formatMessage({ id: 'Colour' }),
        valueGetter: (_, row) => row.colour,
        flex: 1,
      },
      {
        field: 'type',
        headerName: ctIntl.formatMessage({ id: 'Type' }),
        valueGetter: (_, row) => row.fleetVehicleType,
        flex: 1,
      },
      {
        field: 'category',
        headerName: ctIntl.formatMessage({ id: 'Category' }),
        valueGetter: (_, row) => row.bookingVehicleType,
        flex: 1,
      },
      {
        field: 'departments',
        headerName: ctIntl.formatMessage({
          id: isSCDF ? 'Vehicle Groups' : 'Departments',
        }),
        valueGetter: (_, row) => row.departments.map((d) => d.name).join(', '),
        renderCell: ({ value }) => <CellTextWithMore value={value ?? ''} />,
        flex: 1,
      } satisfies GridColDef<AvailableVehicle, string>,
      {
        field: 'siteLocationName',
        headerName: translateFacilitiesTerm('vehicleDetail.defaultFacility'),
        valueGetter: (_, row) => row.siteLocationName,
        flex: 1,
      },
      {
        field: 'commonPool',
        headerName: ctIntl.formatMessage({ id: 'Common Pool Booking' }),
        valueGetter: (_, row) => row.commonPool,
        type: 'boolean',
        renderCell: ({ row }) => (
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Common Pool Booking' })}
            placement="top"
          >
            <Switch
              defaultChecked={row.commonPool}
              checked={row.commonPool}
              disabled
              // disabled={!canEditVehicle}
              onChange={(e) => {
                updateVehicleCommonPoolMutation.mutate({
                  vehicle: row.vehicle,
                  commonPool: e.target.checked,
                })
              }}
            />
          </Tooltip>
        ),
        renderEditCell: ({ row }) => (
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Common Pool Booking' })}
            placement="top"
          >
            <Switch
              defaultChecked={row.commonPool}
              checked={row.commonPool}
              disabled={!canEditVehicle}
              onChange={(e) => {
                apiRef.current?.setEditCellValue({
                  id: row.id,
                  field: 'commonPool',
                  value: e.target.checked,
                })
              }}
            />
          </Tooltip>
        ),
        editable: true,
        flex: 1,
      },
      {
        field: 'availableFor',
        headerName: ctIntl.formatMessage({ id: 'Available For' }),
        valueGetter: (_, row) =>
          row.availableFor
            ?.map(
              (id) =>
                bookingTypesOptions.find((type) => type.id.toString() === id.toString())
                  ?.label ?? '',
            )
            .join(', ') ?? '',
        renderEditCell: ({ row }) => (
          <Autocomplete
            fullWidth
            multiple
            disableCloseOnSelect
            options={bookingTypesOptions}
            value={bookingTypesOptions.filter(
              (type) => row.availableFor?.includes(Number(type.id)),
            )}
            onChange={(_, newValue) => {
              apiRef.current?.setEditCellValue({
                id: row.id,
                field: 'availableFor',
                value: newValue.map((type) => type.id),
              })
              // updateVehicleAvailabilityMutation.mutate({
              //   vehicle: row.vehicle,
              //   availableFor: newValue.map((type) => type.id),
              // })
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label={ctIntl.formatMessage({ id: 'Available For' })}
              />
            )}
          />
        ),
        flex: 2,
        editable: true,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        type: 'actions',
        getActions: ({ row, id }) =>
          rowModesModel[id]?.mode === GridRowModes.Edit
            ? [
                <GridActionsCellItem
                  key="save"
                  icon={<CheckIcon sx={{ color: 'success.main' }} />}
                  label={ctIntl.formatMessage({ id: 'Save' })}
                  onClick={() => handleSaveClick(id)}
                />,
                <GridActionsCellItem
                  key="cancel"
                  icon={<CloseIcon sx={{ color: 'error.main' }} />}
                  label={ctIntl.formatMessage({ id: 'Cancel' })}
                  onClick={() => handleCancelClick(id)}
                />,
              ]
            : [
                <Tooltip
                  key="edit"
                  title={ctIntl.formatMessage({ id: 'Edit Vehicle' })}
                >
                  <span>
                    <GridActionsCellItem
                      icon={<EditOutlinedIcon />}
                      label={ctIntl.formatMessage({ id: 'Edit' })}
                      onClick={() => handleEditClick(id)}
                    />
                  </span>
                </Tooltip>,
                <Tooltip
                  key="view"
                  title={ctIntl.formatMessage({ id: 'View Vehicle' })}
                >
                  <span>
                    <GridActionsCellItem
                      icon={<VisibilityOutlinedIcon />}
                      label={ctIntl.formatMessage({ id: 'View' })}
                      onClick={() =>
                        history.push(
                          getVehicleDetailsModalMainPath(
                            history.location,
                            row.id,
                            'SETTINGS',
                          ),
                        )
                      }
                    />
                  </span>
                </Tooltip>,
              ],
        flex: 1,
      },
    ]
    return allColumns.filter((c) => c.field !== 'actions' || canEditVehicle)
  }, [
    apiRef,
    bookingTypesOptions,
    canEditVehicle,
    handleCancelClick,
    handleEditClick,
    handleSaveClick,
    history,
    isSCDF,
    rowModesModel,
    translateFacilitiesTerm,
    updateVehicleCommonPoolMutation,
  ])

  return (
    <UserDataGridWithSavedSettingsOnIDB
      Component={DataGrid}
      sx={{ '& .MuiDataGrid-row': { cursor: 'pointer' } }}
      dataGridId="vehicleList"
      apiRef={apiRef}
      disableVirtualization
      loading={
        availableVehiclesQuery.isPending ||
        availableVehiclesQuery.isFetching ||
        updateVehicleAvailabilityMutation.isPending
      }
      autoPageSize
      slots={{
        toolbar: KarooToolbar,
        loadingOverlay: LinearProgress,
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            searchFilter: { show: true, props: { quickSearchDebounceMs: 500 } },
            settingsButton: { show: true },
            filterButton: { show: true },
          },
        }),
        basePagination: { material: { showFirstButton: true, showLastButton: true } },
      }}
      columns={columns}
      rows={rows}
      pagination
      editMode="row"
      processRowUpdate={processRowUpdate}
      rowModesModel={rowModesModel}
      onRowModesModelChange={handleRowModesModelChange}
      initialState={{
        columns: {
          columnVisibilityModel: {
            description: false,
            description2: false,
            description3: false,
          },
        },
      }}
    />
  )
}

export default AvailableVehicles

const eqSet = (setA, setB) => {
  if (setA.size !== setB.size) {
    return false
  }
  for (const item of setA) {
    if (!setB.has(item)) {
      return false
    }
  }
  return true
}
